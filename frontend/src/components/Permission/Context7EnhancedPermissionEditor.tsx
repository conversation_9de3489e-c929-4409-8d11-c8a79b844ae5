/**
 * Context7增强版权限编辑器
 * 基于Context7最佳实践实现：
 * 1. Laravel Permission模式：权限优先检查、超级管理员绕过
 * 2. React条件渲染优化：性能优化、状态保持
 * 3. AccessControl.js模式：属性级过滤、资源控制
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Modal,
  Tabs,
  Transfer,
  Table,
  Switch,
  Button,
  Space,
  Typography,
  Tag,
  Tooltip,
  Card,
  Row,
  Col,
  Divider,
  Alert,
  Badge,
  Select,
  Descriptions,
  message,
  Spin
} from 'antd';
import {
  UserOutlined,
  GlobalOutlined,
  CloudServerOutlined,
  FilterOutlined,
  LockOutlined,
  UnlockOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useAdvancedPermissionCheck } from '../../contexts/PermissionContext';
import { PermissionFilter, usePermissionFilter } from '../../utils/permissionFilter';
import { OptimizedPermissionGuard, PermissionRender } from './OptimizedPermissionGuard';
import type { TransferDirection, TransferItem } from 'antd/es/transfer';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 权限项接口定义（增强版）
interface EnhancedPermissionItem extends TransferItem {
  key: string;
  title: string;
  description?: string;
  category: string;
  icon?: React.ReactNode;
  parentCode?: string;
  attributes?: string[];
  level: 'page' | 'module' | 'resource';
  sensitive?: boolean;
}

// 网站权限接口定义（增强版）
interface EnhancedWebsitePermission {
  id: number;
  name: string;
  url: string;
  status: string;
  access_granted: boolean;
  edit_granted: boolean;
  view_credentials: boolean;
  manage_ssl: boolean;
  manage_backup: boolean;
  attributes?: string[];
}

// 服务器权限接口定义（增强版）
interface EnhancedServerPermission {
  id: number;
  name: string;
  ip_address: string;
  status: string;
  access_granted: boolean;
  ssh_access: boolean;
  sudo_access: boolean;
  file_access: boolean;
  monitor_access: boolean;
  attributes?: string[];
}

// 组件属性接口
interface Context7EnhancedPermissionEditorProps {
  visible: boolean;
  userId: number | null;
  onCancel: () => void;
  onSuccess: (permissionData: any) => void;
  mode?: 'view' | 'edit';
  filterLevel?: 'basic' | 'advanced' | 'full';
}

/**
 * Context7增强版权限编辑器组件
 */
export const Context7EnhancedPermissionEditor: React.FC<Context7EnhancedPermissionEditorProps> = React.memo(({
  visible,
  userId,
  onCancel,
  onSuccess,
  mode = 'edit',
  filterLevel = 'advanced'
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('page');
  const [permissionData, setPermissionData] = useState<any>(null);
  
  // 页面权限状态
  const [pagePermissions, setPagePermissions] = useState<EnhancedPermissionItem[]>([]);
  const [pageTargetKeys, setPageTargetKeys] = useState<string[]>([]);
  
  // 网站权限状态
  const [websitePermissions, setWebsitePermissions] = useState<EnhancedWebsitePermission[]>([]);
  
  // 服务器权限状态
  const [serverPermissions, setServerPermissions] = useState<EnhancedServerPermission[]>([]);
  
  // 过滤状态
  const [attributeFilter, setAttributeFilter] = useState<string>('*');
  const [permissionLevel, setPermissionLevel] = useState<'all' | 'page' | 'module' | 'resource'>('all');

  // 权限检查Hook
  const permissionCheck = useAdvancedPermissionCheck({
    permissions: ['user.permission.manage'],
    roles: ['admin', 'super_admin']
  });

  // 权限过滤Hook
  const permissionFilter = usePermissionFilter(
    PermissionFilter.parseAttributeString(attributeFilter)
  );

  // 获取用户权限数据
  const fetchUserPermissions = useCallback(async () => {
    if (!userId) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/permissions/user/${userId}/complete`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('获取用户权限失败');
      }

      const result = await response.json();
      if (result.success) {
        const data = result.data;
        setPermissionData(data);

        // 处理页面权限
        const pagePerms = data.pagePermissions?.map((perm: any) => ({
          key: perm.permission_code,
          title: perm.permission_name,
          description: perm.description,
          category: perm.permission_code.split('.')[0],
          level: perm.permission_code.includes('.') ? 
            (perm.permission_code.split('.').length > 2 ? 'resource' : 'module') : 'page',
          attributes: perm.attributes ? perm.attributes.split(',').map((attr: string) => attr.trim()) : ['*'],
          sensitive: perm.attributes?.includes('!password') || perm.attributes?.includes('!key')
        })) || [];
        setPagePermissions(pagePerms);

        // 设置已授予的页面权限
        const grantedPagePerms = data.pagePermissions?.filter((perm: any) => perm.granted)
          .map((perm: any) => perm.permission_code) || [];
        setPageTargetKeys(grantedPagePerms);

        // 处理网站权限
        const websitePerms = data.websitePermissions?.map((perm: any) => ({
          ...perm,
          attributes: ['name', 'url', 'status', '!password']
        })) || [];
        setWebsitePermissions(websitePerms);

        // 处理服务器权限
        const serverPerms = data.serverPermissions?.map((perm: any) => ({
          ...perm,
          attributes: ['name', 'ip_address', 'status', '!ssh_password']
        })) || [];
        setServerPermissions(serverPerms);

      } else {
        throw new Error(result.error?.message || '获取权限数据失败');
      }
    } catch (error) {
      console.error('获取用户权限失败:', error);
      message.error('获取用户权限失败');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // 组件初始化
  useEffect(() => {
    if (visible && userId) {
      fetchUserPermissions();
    }
  }, [visible, userId, fetchUserPermissions]);

  // 过滤后的页面权限（基于权限级别和属性过滤）
  const filteredPagePermissions = useMemo(() => {
    let filtered = pagePermissions;

    // 按权限级别过滤
    if (permissionLevel !== 'all') {
      filtered = filtered.filter(perm => perm.level === permissionLevel);
    }

    // 按过滤级别过滤
    if (filterLevel === 'basic') {
      filtered = filtered.filter(perm => !perm.sensitive);
    } else if (filterLevel === 'advanced') {
      filtered = filtered.filter(perm => perm.level !== 'resource' || !perm.sensitive);
    }

    return filtered;
  }, [pagePermissions, permissionLevel, filterLevel]);

  // 页面权限变更处理
  const handlePagePermissionChange = useCallback((
    targetKeys: string[],
    direction: TransferDirection,
    moveKeys: string[]
  ) => {
    setPageTargetKeys(targetKeys);
  }, []);

  // Transfer组件渲染函数优化（稳定版）
  const renderTransferItem = useCallback((item: EnhancedPermissionItem) => {
    const getPermissionTypeColor = (level: string) => {
      switch (level) {
        case 'page': return 'blue';
        case 'module': return 'green';
        case 'resource': return 'orange';
        default: return 'default';
      }
    };

    const getPermissionTypeName = (level: string) => {
      switch (level) {
        case 'page': return '页面';
        case 'module': return '模块';
        case 'resource': return '资源';
        default: return level;
      }
    };

    return {
      key: item.key,
      title: (
        <div className="permission-item">
          <div className="permission-header">
            <span className="permission-name">{item.title}</span>
            <Tag color={getPermissionTypeColor(item.level)}>{getPermissionTypeName(item.level)}</Tag>
          </div>
          <div className="permission-description">{item.description}</div>
          <div className="permission-attributes">属性: {item.attributes?.join(', ') || '*'}</div>
        </div>
      ),
    };
  }, []);

  // Transfer组件过滤函数优化（稳定版）
  const filterTransferOption = useCallback((inputValue: string, item: any) => {
    const searchValue = inputValue.toLowerCase();
    return (
      item.title.toLowerCase().includes(searchValue) ||
      item.description?.toLowerCase().includes(searchValue)
    );
  }, []);

  // 网站权限变更处理
  const updateWebsitePermission = useCallback((
    websiteId: number,
    field: keyof EnhancedWebsitePermission,
    value: boolean
  ) => {
    setWebsitePermissions(prev => 
      prev.map(perm => 
        perm.id === websiteId ? { ...perm, [field]: value } : perm
      )
    );
  }, []);

  // 服务器权限变更处理
  const updateServerPermission = useCallback((
    serverId: number,
    field: keyof EnhancedServerPermission,
    value: boolean
  ) => {
    setServerPermissions(prev => 
      prev.map(perm => 
        perm.id === serverId ? { ...perm, [field]: value } : perm
      )
    );
  }, []);

  // 保存权限变更
  const handleSave = useCallback(async () => {
    if (!userId || !permissionCheck.granted) {
      message.error('权限不足，无法保存');
      return;
    }

    setLoading(true);
    try {
      const permissionData = {
        pagePermissions: pageTargetKeys,
        websitePermissions: websitePermissions.map(perm => ({
          website_id: perm.id,
          access_granted: perm.access_granted,
          edit_granted: perm.edit_granted,
          view_credentials: perm.view_credentials,
          manage_ssl: perm.manage_ssl,
          manage_backup: perm.manage_backup
        })),
        serverPermissions: serverPermissions.map(perm => ({
          server_id: perm.id,
          access_granted: perm.access_granted,
          ssh_access: perm.ssh_access,
          sudo_access: perm.sudo_access,
          file_access: perm.file_access,
          monitor_access: perm.monitor_access
        }))
      };

      await onSuccess(permissionData);
      message.success('权限保存成功');
    } catch (error) {
      console.error('保存权限失败:', error);
      message.error('保存权限失败');
    } finally {
      setLoading(false);
    }
  }, [userId, pageTargetKeys, websitePermissions, serverPermissions, onSuccess, permissionCheck.granted]);

  // 权限统计信息
  const permissionStats = useMemo(() => {
    const totalPage = filteredPagePermissions.length;
    const grantedPage = pageTargetKeys.length;
    const totalWebsite = websitePermissions.length;
    const grantedWebsite = websitePermissions.filter(perm => perm.access_granted).length;
    const totalServer = serverPermissions.length;
    const grantedServer = serverPermissions.filter(perm => perm.access_granted).length;

    return {
      page: { total: totalPage, granted: grantedPage },
      website: { total: totalWebsite, granted: grantedWebsite },
      server: { total: totalServer, granted: grantedServer }
    };
  }, [filteredPagePermissions, pageTargetKeys, websitePermissions, serverPermissions]);

  return (
    <OptimizedPermissionGuard
      permissions={['user.permission.manage']}
      mode="show-error"
      errorType="result"
    >
      <Modal
        title={
          <Space>
            <UserOutlined />
            <span>权限管理</span>
            <Badge count={permissionStats.page.granted + permissionStats.website.granted + permissionStats.server.granted} />
          </Space>
        }
        open={visible}
        onCancel={onCancel}
        width={1200}
        footer={
          <PermissionRender permission="user.permission.manage">
            <Space>
              <Button onClick={onCancel}>取消</Button>
              <Button 
                type="primary" 
                onClick={handleSave} 
                loading={loading}
                disabled={mode === 'view'}
              >
                保存权限
              </Button>
            </Space>
          </PermissionRender>
        }
      >
        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <p style={{ marginTop: '16px' }}>加载权限数据中...</p>
          </div>
        ) : (
          <>
            {/* 权限统计卡片 */}
            <Row gutter={16} style={{ marginBottom: '16px' }}>
              <Col span={8}>
                <Card size="small">
                  <Descriptions size="small" column={1}>
                    <Descriptions.Item label="页面权限">
                      <Badge count={permissionStats.page.granted} />
                      <span style={{ marginLeft: '8px' }}>/ {permissionStats.page.total}</span>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Descriptions size="small" column={1}>
                    <Descriptions.Item label="网站权限">
                      <Badge count={permissionStats.website.granted} />
                      <span style={{ marginLeft: '8px' }}>/ {permissionStats.website.total}</span>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Descriptions size="small" column={1}>
                    <Descriptions.Item label="服务器权限">
                      <Badge count={permissionStats.server.granted} />
                      <span style={{ marginLeft: '8px' }}>/ {permissionStats.server.total}</span>
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </Col>
            </Row>

            {/* 过滤控制 */}
            <Card size="small" style={{ marginBottom: '16px' }}>
              <Row gutter={16} align="middle">
                <Col span={6}>
                  <Space>
                    <FilterOutlined />
                    <Text strong>权限过滤:</Text>
                  </Space>
                </Col>
                <Col span={6}>
                  <Select
                    value={permissionLevel}
                    onChange={setPermissionLevel}
                    style={{ width: '100%' }}
                    size="small"
                  >
                    <Option value="all">全部权限</Option>
                    <Option value="page">页面级权限</Option>
                    <Option value="module">模块级权限</Option>
                    <Option value="resource">资源级权限</Option>
                  </Select>
                </Col>
                <Col span={6}>
                  <Select
                    value={attributeFilter}
                    onChange={setAttributeFilter}
                    style={{ width: '100%' }}
                    size="small"
                  >
                    <Option value="*">全部属性</Option>
                    <Option value="name,status">基础属性</Option>
                    <Option value="*,!password,!key">排除敏感信息</Option>
                  </Select>
                </Col>
                <Col span={6}>
                  <Space>
                    {filterLevel === 'basic' && <Tag color="green">基础模式</Tag>}
                    {filterLevel === 'advanced' && <Tag color="blue">高级模式</Tag>}
                    {filterLevel === 'full' && <Tag color="red">完整模式</Tag>}
                  </Space>
                </Col>
              </Row>
            </Card>

            {/* 权限标签页 */}
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane
                tab={
                  <Space>
                    <UserOutlined />
                    <span>页面权限</span>
                    <Badge count={permissionStats.page.granted} size="small" />
                  </Space>
                }
                key="page"
              >
                <Transfer
                  dataSource={filteredPagePermissions}
                  targetKeys={pageTargetKeys}
                  onChange={handlePagePermissionChange}
                  render={renderTransferItem}
                  titles={['可用权限', '已授权限']}
                  showSearch
                  filterOption={filterTransferOption}
                  listStyle={{
                    width: 400,
                    height: 400,
                  }}
                  operations={['授予', '撤销']}
                  disabled={mode === 'view' || !permissionCheck.granted}
                />
              </TabPane>

              <TabPane
                tab={
                  <Space>
                    <GlobalOutlined />
                    <span>网站权限</span>
                    <Badge count={permissionStats.website.granted} size="small" />
                  </Space>
                }
                key="website"
              >
                <Table
                  dataSource={websitePermissions}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                  scroll={{ y: 400 }}
                  size="small"
                  columns={[
                    {
                      title: '网站名称',
                      dataIndex: 'name',
                      key: 'name',
                      width: 150,
                      render: (name, record) => (
                        <Space direction="vertical" size="small">
                          <Text strong>{permissionFilter.filterObject(record, ['name']).name}</Text>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {permissionFilter.filterObject(record, ['url']).url}
                          </Text>
                        </Space>
                      )
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      width: 80,
                      render: (status) => (
                        <Tag color={status === 'active' ? 'green' : 'red'}>
                          {permissionFilter.filterObject({ status }, ['status']).status}
                        </Tag>
                      )
                    },
                    {
                      title: '访问权限',
                      dataIndex: 'access_granted',
                      key: 'access_granted',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateWebsitePermission(record.id, 'access_granted', checked)}
                            disabled={mode === 'view'}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: '编辑权限',
                      dataIndex: 'edit_granted',
                      key: 'edit_granted',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateWebsitePermission(record.id, 'edit_granted', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: '查看密码',
                      dataIndex: 'view_credentials',
                      key: 'view_credentials',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateWebsitePermission(record.id, 'view_credentials', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: 'SSL管理',
                      dataIndex: 'manage_ssl',
                      key: 'manage_ssl',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateWebsitePermission(record.id, 'manage_ssl', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: '备份管理',
                      dataIndex: 'manage_backup',
                      key: 'manage_backup',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateWebsitePermission(record.id, 'manage_backup', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    }
                  ]}
                />
              </TabPane>

              <TabPane
                tab={
                  <Space>
                    <CloudServerOutlined />
                    <span>服务器权限</span>
                    <Badge count={permissionStats.server.granted} size="small" />
                  </Space>
                }
                key="server"
              >
                <Table
                  dataSource={serverPermissions}
                  rowKey="id"
                  pagination={{ pageSize: 10 }}
                  scroll={{ y: 400 }}
                  size="small"
                  columns={[
                    {
                      title: '服务器信息',
                      key: 'server_info',
                      width: 200,
                      render: (_, record) => (
                        <Space direction="vertical" size="small">
                          <Text strong>{permissionFilter.filterObject(record, ['name']).name}</Text>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {permissionFilter.filterObject(record, ['ip_address']).ip_address}
                          </Text>
                        </Space>
                      )
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      width: 80,
                      render: (status) => (
                        <Tag color={status === 'online' ? 'green' : 'red'}>
                          {permissionFilter.filterObject({ status }, ['status']).status}
                        </Tag>
                      )
                    },
                    {
                      title: '访问权限',
                      dataIndex: 'access_granted',
                      key: 'access_granted',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateServerPermission(record.id, 'access_granted', checked)}
                            disabled={mode === 'view'}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: 'SSH访问',
                      dataIndex: 'ssh_access',
                      key: 'ssh_access',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateServerPermission(record.id, 'ssh_access', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: 'Sudo权限',
                      dataIndex: 'sudo_access',
                      key: 'sudo_access',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateServerPermission(record.id, 'sudo_access', checked)}
                            disabled={mode === 'view' || !record.ssh_access}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: '文件访问',
                      dataIndex: 'file_access',
                      key: 'file_access',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateServerPermission(record.id, 'file_access', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    },
                    {
                      title: '监控访问',
                      dataIndex: 'monitor_access',
                      key: 'monitor_access',
                      width: 100,
                      render: (granted, record) => (
                        <PermissionRender permission="user.permission.manage">
                          <Switch
                            checked={granted}
                            onChange={(checked) => updateServerPermission(record.id, 'monitor_access', checked)}
                            disabled={mode === 'view' || !record.access_granted}
                            size="small"
                          />
                        </PermissionRender>
                      )
                    }
                  ]}
                />
              </TabPane>
            </Tabs>
          </>
        )}
      </Modal>
    </OptimizedPermissionGuard>
  );
}, (prevProps, nextProps) => {
  // 自定义比较函数，只有关键属性变化时才重新渲染
  return (
    prevProps.visible === nextProps.visible &&
    prevProps.userId === nextProps.userId &&
    prevProps.mode === nextProps.mode &&
    prevProps.filterLevel === nextProps.filterLevel &&
    prevProps.onCancel === nextProps.onCancel &&
    prevProps.onSuccess === nextProps.onSuccess
  );
});

export default Context7EnhancedPermissionEditor;
