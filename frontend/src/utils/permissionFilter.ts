/**
 * 权限属性过滤工具
 * 基于AccessControl.js最佳实践实现
 * 功能：
 * 1. 属性级别的访问控制
 * 2. 支持通配符和拒绝模式
 * 3. 嵌套对象属性过滤
 * 4. 数组数据批量过滤
 */

/**
 * 权限过滤器类
 */
export class PermissionFilter {
  /**
   * 过滤单个对象的属性
   * @param data 要过滤的数据对象
   * @param attributes 允许的属性列表，支持通配符(*)和拒绝模式(!)
   * @returns 过滤后的对象
   */
  static filterObject(data: any, attributes: string[] = ['*']): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    // 如果包含通配符，返回所有数据（除了被明确拒绝的）
    if (attributes.includes('*')) {
      const deniedAttrs = attributes.filter(attr => attr.startsWith('!'));
      if (deniedAttrs.length === 0) {
        return data;
      }
      
      // 过滤掉被拒绝的属性
      const filtered = { ...data };
      deniedAttrs.forEach(attr => {
        const key = attr.substring(1); // 移除 '!' 前缀
        this.deleteNestedProperty(filtered, key);
      });
      return filtered;
    }

    // 只返回明确允许的属性
    const filtered: any = {};
    attributes.forEach(attr => {
      if (!attr.startsWith('!')) {
        const value = this.getNestedProperty(data, attr);
        if (value !== undefined) {
          this.setNestedProperty(filtered, attr, value);
        }
      }
    });
    return filtered;
  }

  /**
   * 过滤数组中的对象
   * @param dataArray 要过滤的数据数组
   * @param attributes 允许的属性列表
   * @returns 过滤后的数组
   */
  static filterArray(dataArray: any[], attributes: string[] = ['*']): any[] {
    if (!Array.isArray(dataArray)) {
      return dataArray;
    }

    return dataArray.map(item => this.filterObject(item, attributes));
  }

  /**
   * 获取嵌套属性值
   * @param obj 对象
   * @param path 属性路径，如 'user.profile.name'
   * @returns 属性值
   */
  private static getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 设置嵌套属性值
   * @param obj 对象
   * @param path 属性路径
   * @param value 要设置的值
   */
  private static setNestedProperty(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }

  /**
   * 删除嵌套属性
   * @param obj 对象
   * @param path 属性路径
   */
  private static deleteNestedProperty(obj: any, path: string): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    const target = keys.reduce((current, key) => {
      return current && current[key] ? current[key] : null;
    }, obj);
    
    if (target && target[lastKey] !== undefined) {
      delete target[lastKey];
    }
  }

  /**
   * 检查属性是否被允许
   * @param attribute 属性名
   * @param allowedAttributes 允许的属性列表
   * @returns 是否被允许
   */
  static isAttributeAllowed(attribute: string, allowedAttributes: string[] = ['*']): boolean {
    // 检查是否被明确拒绝
    if (allowedAttributes.some(attr => attr === `!${attribute}`)) {
      return false;
    }

    // 检查是否有通配符
    if (allowedAttributes.includes('*')) {
      return true;
    }

    // 检查是否被明确允许
    return allowedAttributes.includes(attribute);
  }

  /**
   * 合并属性列表
   * @param baseAttributes 基础属性列表
   * @param additionalAttributes 额外属性列表
   * @returns 合并后的属性列表
   */
  static mergeAttributes(baseAttributes: string[], additionalAttributes: string[]): string[] {
    const merged = [...baseAttributes];
    
    additionalAttributes.forEach(attr => {
      if (!merged.includes(attr)) {
        merged.push(attr);
      }
    });
    
    return merged;
  }

  /**
   * 解析属性字符串
   * @param attributeString 属性字符串，如 "name,email,!password"
   * @returns 属性数组
   */
  static parseAttributeString(attributeString: string): string[] {
    if (!attributeString || attributeString.trim() === '') {
      return ['*'];
    }
    
    return attributeString
      .split(',')
      .map(attr => attr.trim())
      .filter(attr => attr.length > 0);
  }
}

/**
 * 权限过滤Hook
 * 在React组件中使用的便捷Hook
 * 优化版：使用useMemo和useCallback避免不必要的重新渲染
 */
export const usePermissionFilter = (attributes: string[] = ['*']) => {
  // 导入React hooks
  const { useMemo, useCallback } = require('react');

  // 使用useMemo缓存属性相关的计算结果
  const allowedAttributes = useMemo(() =>
    attributes.filter(attr => !attr.startsWith('!')),
    [attributes]
  );

  const deniedAttributes = useMemo(() =>
    attributes
      .filter(attr => attr.startsWith('!'))
      .map(attr => attr.substring(1)),
    [attributes]
  );

  // 使用useCallback缓存函数，避免每次渲染都创建新函数
  const filterObject = useCallback((data: any) =>
    PermissionFilter.filterObject(data, attributes),
    [attributes]
  );

  const filterArray = useCallback((dataArray: any[]) =>
    PermissionFilter.filterArray(dataArray, attributes),
    [attributes]
  );

  const isAllowed = useCallback((attribute: string) =>
    PermissionFilter.isAttributeAllowed(attribute, attributes),
    [attributes]
  );

  const renderIfAllowed = useCallback((attribute: string, component: React.ReactNode) =>
    PermissionFilter.isAttributeAllowed(attribute, attributes) ? component : null,
    [attributes]
  );

  const getAllowedAttributes = useCallback(() => allowedAttributes, [allowedAttributes]);

  const getDeniedAttributes = useCallback(() => deniedAttributes, [deniedAttributes]);

  // 使用useMemo缓存返回的对象，只有当attributes变化时才重新创建
  return useMemo(() => ({
    /**
     * 过滤单个对象
     */
    filterObject,

    /**
     * 过滤数组
     */
    filterArray,

    /**
     * 检查属性是否被允许
     */
    isAllowed,

    /**
     * 条件渲染：只有当属性被允许时才渲染
     */
    renderIfAllowed,

    /**
     * 获取过滤后的属性列表
     */
    getAllowedAttributes,

    /**
     * 获取被拒绝的属性列表
     */
    getDeniedAttributes
  }), [filterObject, filterArray, isAllowed, renderIfAllowed, getAllowedAttributes, getDeniedAttributes]);
};

/**
 * 权限过滤装饰器
 * 用于类方法的权限过滤
 */
export function withPermissionFilter(attributes: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const result = method.apply(this, args);
      
      // 如果结果是Promise，异步过滤
      if (result && typeof result.then === 'function') {
        return result.then((data: any) => PermissionFilter.filterObject(data, attributes));
      }
      
      // 同步过滤
      return PermissionFilter.filterObject(result, attributes);
    };
    
    return descriptor;
  };
}

/**
 * 默认导出权限过滤器
 */
export default PermissionFilter;
