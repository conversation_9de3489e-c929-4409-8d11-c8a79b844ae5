const jwt = require('jsonwebtoken');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key-change-this-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

// 认证中间件
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问被拒绝：需要认证令牌',
      timestamp: new Date().toISOString()
    });
  }

  // 开发模式下允许dev-token
  if (process.env.NODE_ENV === 'development' && token === 'dev-token') {
    req.user = {
      id: 1,
      username: 'dev-user',
      role: 'admin'
    };
    return next();
  }

  // 尝试解析JWT token
  try {
    const user = jwt.verify(token, JWT_SECRET);
    req.user = user;
    return next();
  } catch (jwtErr) {
    // 如果JWT验证失败，尝试解析base64 token（兼容现有登录系统）
    try {
      const decoded = Buffer.from(token, 'base64').toString('utf-8');
      const parts = decoded.split(':');

      if (parts.length >= 2) {
        const userId = parseInt(parts[0]);
        const username = parts[1];

        if (!isNaN(userId) && username) {
          // 从数据库查询用户真实角色
          const { pool } = require('./utils/db');

          try {
            const [rows] = await pool.execute(
              'SELECT role FROM users WHERE id = ? AND username = ?',
              [userId, username]
            );

            const userRole = rows.length > 0 ? rows[0].role : 'user';

            req.user = {
              id: userId,
              username: username,
              role: userRole
            };

            return next();
          } catch (dbErr) {
            console.error('数据库查询用户角色失败:', dbErr);
            // 如果数据库查询失败，使用默认角色
            req.user = {
              id: userId,
              username: username,
              role: 'user'
            };
            return next();
          }
        }
      }
    } catch (decodeErr) {
      // base64解码失败，继续返回原始错误
    }

    return res.status(403).json({
      success: false,
      message: '无效的认证令牌',
      timestamp: new Date().toISOString()
    });
  }
};

// 可选认证中间件（用于某些可以匿名访问的接口）
const optionalAuth = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    jwt.verify(token, JWT_SECRET, (err, user) => {
      if (!err) {
        req.user = user;
      }
    });
  }
  next();
};

// 登录API处理函数 - 使用数据库认证
const handleLogin = async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空',
        timestamp: new Date().toISOString()
      });
    }

    // 获取数据库连接 - 使用与simple-server.js相同的配置
    const mysql = require('mysql2/promise');
    const dbConfig = {
      host: 'localhost',
      port: 3306,
      user: 'sitemanager',
      password: 'sitemanager123',
      database: 'sitemanager',
      charset: 'utf8mb4',
      timezone: '+08:00'
    };

    const db = mysql.createPool(dbConfig);

    try {
      // 从数据库查询用户
      const [users] = await db.execute(
        'SELECT id, username, password, email, real_name, role, status, avatar, phone, department, last_login, created_at, updated_at FROM users WHERE username = ? AND status = "active"',
        [username]
      );

      if (users.length === 0) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误',
          timestamp: new Date().toISOString()
        });
      }

      const user = users[0];

      // 验证密码（支持明文密码和bcrypt加密密码）
      let isPasswordValid = false;
      
      if (user.password.startsWith('$2')) {
        // bcrypt加密密码
        const bcrypt = require('bcryptjs');
        isPasswordValid = await bcrypt.compare(password, user.password);
      } else {
        // 明文密码（临时兼容）
        isPasswordValid = user.password === password;
        
        // 如果是明文密码且验证成功，自动升级为加密密码
        if (isPasswordValid) {
          const bcrypt = require('bcryptjs');
          const hashedPassword = await bcrypt.hash(password, 12);
          await db.execute(
            'UPDATE users SET password = ? WHERE id = ?',
            [hashedPassword, user.id]
          );
          console.log(`用户 ${username} 的密码已自动升级为加密存储`);
        }
      }

      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误',
          timestamp: new Date().toISOString()
        });
      }

      // 更新最后登录时间
      await db.execute(
        'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
        [user.id]
      );

      // 生成JWT token - 使用数字ID
      const token = jwt.sign(
        {
          id: user.id,  // 使用数字ID而不是用户名
          username: user.username,
          role: user.role
        },
        JWT_SECRET,
        { expiresIn: JWT_EXPIRES_IN }
      );

      // 生成refresh token
      const refreshToken = jwt.sign(
        {
          id: user.id,
          username: user.username,
          type: 'refresh'
        },
        JWT_SECRET,
        { expiresIn: JWT_REFRESH_EXPIRES_IN }
      );

      // 返回完整的用户信息（不包括密码）
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        realName: user.real_name,
        role: user.role,
        status: user.status,
        avatar: user.avatar,
        phone: user.phone,
        department: user.department,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      };

      console.log('🔍 数据库认证成功:', {
        userId: user.id,
        username: user.username,
        role: user.role,
        status: user.status
      });

      res.json({
        success: true,
        message: '登录成功',
        data: {
          token,
          refreshToken,
          user: userResponse,
          expiresIn: JWT_EXPIRES_IN
        },
        timestamp: new Date().toISOString()
      });

    } finally {
      // 关闭数据库连接
      await db.end();
    }

  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败: ' + error.message,
      timestamp: new Date().toISOString()
    });
  }
};

// 验证token有效性
const handleVerify = (req, res) => {
  res.json({
    success: true,
    message: 'Token有效',
    data: {
      user: req.user
    },
    timestamp: new Date().toISOString()
  });
};

// 用户登出
const handleLogout = (req, res) => {
  res.json({
    success: true,
    message: '登出成功',
    timestamp: new Date().toISOString()
  });
};

// 刷新token处理
const handleRefresh = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: '缺少刷新令牌',
        timestamp: new Date().toISOString()
      });
    }

    // 验证refresh token
    jwt.verify(refreshToken, JWT_SECRET, async (err, decoded) => {
      if (err) {
        return res.status(403).json({
          success: false,
          message: '刷新令牌无效或已过期',
          timestamp: new Date().toISOString()
        });
      }

      try {
        // 从数据库获取用户信息
        const mysql = require('mysql2/promise');
        const db = await mysql.createConnection({
          host: process.env.DB_HOST || 'localhost',
          port: process.env.DB_PORT || 3306,
          user: process.env.DB_USER || 'root',
          password: process.env.DB_PASSWORD || '',
          database: process.env.DB_NAME || 'sitemanager',
          charset: 'utf8mb4'
        });

        const [users] = await db.execute(
          'SELECT id, username, email, real_name, role, status FROM users WHERE id = ? AND status = "active"',
          [decoded.id]
        );

        if (users.length === 0) {
          await db.end();
          return res.status(404).json({
            success: false,
            message: '用户不存在或已被禁用',
            timestamp: new Date().toISOString()
          });
        }

        const user = users[0];

        // 生成新的access token
        const newToken = jwt.sign(
          {
            id: user.id,
            username: user.username,
            role: user.role
          },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRES_IN }
        );

        // 生成新的refresh token
        const newRefreshToken = jwt.sign(
          {
            id: user.id,
            username: user.username,
            type: 'refresh'
          },
          JWT_SECRET,
          { expiresIn: JWT_REFRESH_EXPIRES_IN }
        );

        await db.end();

        res.json({
          success: true,
          message: '令牌刷新成功',
          data: {
            token: newToken,
            refreshToken: newRefreshToken,
            user: {
              id: user.id,
              username: user.username,
              email: user.email,
              realName: user.real_name,
              role: user.role,
              status: user.status
            },
            expiresIn: JWT_EXPIRES_IN
          },
          timestamp: new Date().toISOString()
        });

      } catch (dbError) {
        console.error('刷新令牌时数据库错误:', dbError);
        res.status(500).json({
          success: false,
          message: '刷新令牌失败: ' + dbError.message,
          timestamp: new Date().toISOString()
        });
      }
    });

  } catch (error) {
    console.error('刷新令牌失败:', error);
    res.status(500).json({
      success: false,
      message: '刷新令牌失败: ' + error.message,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  JWT_SECRET,
  JWT_EXPIRES_IN,
  JWT_REFRESH_EXPIRES_IN,
  authenticateToken,
  optionalAuth,
  handleLogin,
  handleVerify,
  handleLogout,
  handleRefresh
};
